{"timestamp": "20250528_154802", "summary": {"total_categories": 8, "total_operators": 91, "overall_cpp_avg_time": 11568.572125296705, "overall_python_avg_time": 49139.9208871791, "overall_avg_speedup": 10.209761532462608, "max_speedup": 67.75144779424043, "min_speedup": 0.01689226828764027}, "category_summaries": {"core_math": {"total_operators": 17, "cpp_avg_time": 2149.199378411765, "python_avg_time": 2291.811975267003, "avg_speedup": 2.2298166713107666, "max_speedup": 8.870788649860723, "min_speedup": 0.01689226828764027}, "logical_ops": {"total_operators": 4, "cpp_avg_time": 469.76160000000004, "python_avg_time": 12619.837032010159, "avg_speedup": 26.82262614940247, "max_speedup": 32.650379294854446, "min_speedup": 23.670633068741708}, "comparison_ops": {"total_operators": 6, "cpp_avg_time": 303.67186100000004, "python_avg_time": 3613.2632030381096, "avg_speedup": 12.100246667668912, "max_speedup": 15.535160150227545, "min_speedup": 4.700631064070906}, "data_utils": {"total_operators": 3, "cpp_avg_time": 124.75377800000001, "python_avg_time": 1615.9721650183199, "avg_speedup": 14.700356758290752, "max_speedup": 20.150967025379593, "min_speedup": 3.8470191401263465}, "reduction_ops": {"total_operators": 2, "cpp_avg_time": 304.82, "python_avg_time": 12032.375453660885, "avg_speedup": 39.473776332307686, "max_speedup": 39.547906331790244, "min_speedup": 39.39964633282513}, "timeseries_ops": {"total_operators": 33, "cpp_avg_time": 21998.304853545455, "python_avg_time": 97741.16572707589, "avg_speedup": 13.77383595638731, "max_speedup": 67.75144779424043, "min_speedup": 0.962005846683019}, "panel_ops": {"total_operators": 23, "cpp_avg_time": 10400.912042130434, "python_avg_time": 28938.75186210093, "avg_speedup": 4.285545184968567, "max_speedup": 11.998001693352304, "min_speedup": 0.692623055510354}, "group_ops": {"total_operators": 3, "cpp_avg_time": 15451.219311, "python_avg_time": 146883.54686109556, "avg_speedup": 11.71222756121508, "max_speedup": 16.562141453494625, "min_speedup": 7.323611985422461}}, "detailed_results": [{"Operator": "Abs", "C++_Time_μs": 224.923533, "Python_Time_μs": 1995.2491236229737, "Speedup": 8.870788649860723, "Performance_Ratio": "8.87x", "Category": "core_math"}, {"Operator": "Sign", "C++_Time_μs": 551.997433, "Python_Time_μs": 2665.960074712833, "Speedup": 4.829660276178917, "Performance_Ratio": "4.83x", "Category": "core_math"}, {"Operator": "SignedPower", "C++_Time_μs": 2652.455333, "Python_Time_μs": 7845.754610995452, "Speedup": 2.9579214825539353, "Performance_Ratio": "2.96x", "Category": "core_math"}, {"Operator": "Minus", "C++_Time_μs": 222.146267, "Python_Time_μs": 508.19556539257366, "Speedup": 2.2876619636942794, "Performance_Ratio": "2.29x", "Category": "core_math"}, {"Operator": "Multiply", "C++_Time_μs": 222.538633, "Python_Time_μs": 505.7239904999733, "Speedup": 2.272522229881646, "Performance_Ratio": "2.27x", "Category": "core_math"}, {"Operator": "Add", "C++_Time_μs": 239.8093, "Python_Time_μs": 514.9943133195242, "Speedup": 2.147516019268328, "Performance_Ratio": "2.15x", "Category": "core_math"}, {"Operator": "Softsign", "C++_Time_μs": 1046.920967, "Python_Time_μs": 2228.2544833918414, "Speedup": 2.1283884396517596, "Performance_Ratio": "2.13x", "Category": "core_math"}, {"Operator": "Divide", "C++_Time_μs": 374.811967, "Python_Time_μs": 649.7255526483059, "Speedup": 1.733470672904918, "Performance_Ratio": "1.73x", "Category": "core_math"}, {"Operator": "Log", "C++_Time_μs": 4860.604833, "Python_Time_μs": 8172.958871970574, "Speedup": 1.6814695192832958, "Performance_Ratio": "1.68x", "Category": "core_math"}, {"Operator": "Reverse", "C++_Time_μs": 167.867433, "Python_Time_μs": 257.9285763204098, "Speedup": 1.5365015816999463, "Performance_Ratio": "1.54x", "Category": "core_math"}, {"Operator": "Ceil", "C++_Time_μs": 164.515267, "Python_Time_μs": 214.7803083062172, "Speedup": 1.3055342049575083, "Performance_Ratio": "1.31x", "Category": "core_math"}, {"Operator": "Floor", "C++_Time_μs": 162.825467, "Python_Time_μs": 211.23910943667093, "Speedup": 1.2973345836414578, "Performance_Ratio": "1.30x", "Category": "core_math"}, {"Operator": "Exp", "C++_Time_μs": 9092.607867, "Python_Time_μs": 11549.172488351664, "Speedup": 1.270171622628457, "Performance_Ratio": "1.27x", "Category": "core_math"}, {"Operator": "inv", "C++_Time_μs": 370.939433, "Python_Time_μs": 456.93110053737956, "Speedup": 1.2318213160620741, "Performance_Ratio": "1.23x", "Category": "core_math"}, {"Operator": "Round", "C++_Time_μs": 168.205533, "Python_Time_μs": 200.50043240189552, "Speedup": 1.1919966532961523, "Performance_Ratio": "1.19x", "Category": "core_math"}, {"Operator": "Sqrt", "C++_Time_μs": 630.726667, "Python_Time_μs": 723.5897704958916, "Speedup": 1.1472319284319898, "Performance_Ratio": "1.15x", "Category": "core_math"}, {"Operator": "Power", "C++_Time_μs": 15382.4935, "Python_Time_μs": 259.8452071348826, "Speedup": 0.01689226828764027, "Performance_Ratio": "0.02x", "Category": "core_math"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 503.120167, "Python_Time_μs": 16427.06428344051, "Speedup": 32.650379294854446, "Performance_Ratio": "32.65x", "Category": "logical_ops"}, {"Operator": "Not", "C++_Time_μs": 369.216633, "Python_Time_μs": 9690.921008586884, "Speedup": 26.247249290599765, "Performance_Ratio": "26.25x", "Category": "logical_ops"}, {"Operator": "And", "C++_Time_μs": 505.804767, "Python_Time_μs": 12504.628331710896, "Speedup": 24.722242943413963, "Performance_Ratio": "24.72x", "Category": "logical_ops"}, {"Operator": "Or", "C++_Time_μs": 500.904833, "Python_Time_μs": 11856.734504302343, "Speedup": 23.670633068741708, "Performance_Ratio": "23.67x", "Category": "logical_ops"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 275.8109, "Python_Time_μs": 4284.766502678394, "Speedup": 15.535160150227545, "Performance_Ratio": "15.54x", "Category": "comparison_ops"}, {"Operator": "<PERSON><PERSON><PERSON>", "C++_Time_μs": 276.794133, "Python_Time_μs": 4064.9181542297206, "Speedup": 14.685709231523779, "Performance_Ratio": "14.69x", "Category": "comparison_ops"}, {"Operator": "<PERSON><PERSON>", "C++_Time_μs": 302.909067, "Python_Time_μs": 4159.396172811587, "Speedup": 13.731501054115318, "Performance_Ratio": "13.73x", "Category": "comparison_ops"}, {"Operator": "Mthan", "C++_Time_μs": 309.119533, "Python_Time_μs": 4186.578591664632, "Speedup": 13.543558865510553, "Performance_Ratio": "13.54x", "Category": "comparison_ops"}, {"Operator": "UnEqual", "C++_Time_μs": 331.9847, "Python_Time_μs": 3454.2741253972054, "Speedup": 10.40491964056538, "Performance_Ratio": "10.40x", "Category": "comparison_ops"}, {"Operator": "Equal", "C++_Time_μs": 325.412833, "Python_Time_μs": 1529.645671447118, "Speedup": 4.700631064070906, "Performance_Ratio": "4.70x", "Category": "comparison_ops"}, {"Operator": "getInf", "C++_Time_μs": 103.867867, "Python_Time_μs": 2093.037962913513, "Speedup": 20.150967025379593, "Performance_Ratio": "20.15x", "Category": "data_utils"}, {"Operator": "get<PERSON><PERSON>", "C++_Time_μs": 105.478767, "Python_Time_μs": 2120.4485247532525, "Speedup": 20.103084109366318, "Performance_Ratio": "20.10x", "Category": "data_utils"}, {"Operator": "FilterInf", "C++_Time_μs": 164.9147, "Python_Time_μs": 634.4300073881944, "Speedup": 3.8470191401263465, "Performance_Ratio": "3.85x", "Category": "data_utils"}, {"Operator": "Min", "C++_Time_μs": 304.536067, "Python_Time_μs": 12043.763852367798, "Speedup": 39.547906331790244, "Performance_Ratio": "39.55x", "Category": "reduction_ops"}, {"Operator": "Max", "C++_Time_μs": 305.103933, "Python_Time_μs": 12020.987054953972, "Speedup": 39.39964633282513, "Performance_Ratio": "39.40x", "Category": "reduction_ops"}, {"Operator": "ts_Regression_A", "C++_Time_μs": 2204.599767, "Python_Time_μs": 149364.82602109513, "Speedup": 67.75144779424043, "Performance_Ratio": "67.75x", "Category": "timeseries_ops"}, {"Operator": "ts_Regression_B", "C++_Time_μs": 2562.252433, "Python_Time_μs": 150751.52153149247, "Speedup": 58.83554625216448, "Performance_Ratio": "58.84x", "Category": "timeseries_ops"}, {"Operator": "ts_Regression_D", "C++_Time_μs": 2854.744633, "Python_Time_μs": 153490.45591428876, "Speedup": 53.766790255066844, "Performance_Ratio": "53.77x", "Category": "timeseries_ops"}, {"Operator": "ts_Regression_C", "C++_Time_μs": 2871.467633, "Python_Time_μs": 152903.42839434743, "Speedup": 53.24922580952087, "Performance_Ratio": "53.25x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>rr", "C++_Time_μs": 5029.5987, "Python_Time_μs": 200357.3078662157, "Speedup": 39.83564491262806, "Performance_Ratio": "39.84x", "Category": "timeseries_ops"}, {"Operator": "ts_Cov", "C++_Time_μs": 2760.712133, "Python_Time_μs": 108530.66146373749, "Speedup": 39.312560033486655, "Performance_Ratio": "39.31x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON>_corr", "C++_Time_μs": 21059.485167, "Python_Time_μs": 617514.467642953, "Speedup": 29.32239144243621, "Performance_Ratio": "29.32x", "Category": "timeseries_ops"}, {"Operator": "ts_Sum", "C++_Time_μs": 3012.201733, "Python_Time_μs": 35322.20181077719, "Speedup": 11.726373245127267, "Performance_Ratio": "11.73x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 277.4493, "Python_Time_μs": 2457.604929804802, "Speedup": 8.857852334840283, "Performance_Ratio": "8.86x", "Category": "timeseries_ops"}, {"Operator": "ts_Mean", "C++_Time_μs": 4157.204167, "Python_Time_μs": 36122.90356929103, "Speedup": 8.689230097486101, "Performance_Ratio": "8.69x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON>", "C++_Time_μs": 275.063967, "Python_Time_μs": 2142.438447723786, "Speedup": 7.7888735158239975, "Performance_Ratio": "7.79x", "Category": "timeseries_ops"}, {"Operator": "ts_Scale", "C++_Time_μs": 9347.228333, "Python_Time_μs": 70533.33716467023, "Speedup": 7.545909295449136, "Performance_Ratio": "7.55x", "Category": "timeseries_ops"}, {"Operator": "ts_Stdev", "C++_Time_μs": 6570.750733, "Python_Time_μs": 47429.919646432005, "Speedup": 7.218341034948526, "Performance_Ratio": "7.22x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON><PERSON>", "C++_Time_μs": 9143.506567, "Python_Time_μs": 51186.38056640824, "Speedup": 5.5981127362171925, "Performance_Ratio": "5.60x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON><PERSON>", "C++_Time_μs": 9314.407967, "Python_Time_μs": 50438.65550930301, "Speedup": 5.415122001097873, "Performance_Ratio": "5.42x", "Category": "timeseries_ops"}, {"Operator": "ts_Skewness", "C++_Time_μs": 8578.3844, "Python_Time_μs": 46339.02168522278, "Speedup": 5.401835535048159, "Performance_Ratio": "5.40x", "Category": "timeseries_ops"}, {"Operator": "ts_ChgRate", "C++_Time_μs": 776.487033, "Python_Time_μs": 4104.720118145148, "Speedup": 5.286270013146695, "Performance_Ratio": "5.29x", "Category": "timeseries_ops"}, {"Operator": "ts_Divide", "C++_Time_μs": 745.920067, "Python_Time_μs": 3069.16960204641, "Speedup": 4.114609242770794, "Performance_Ratio": "4.11x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON><PERSON>", "C++_Time_μs": 11766.936267, "Python_Time_μs": 47508.986884107195, "Speedup": 4.037498445312791, "Performance_Ratio": "4.04x", "Category": "timeseries_ops"}, {"Operator": "ts_Product", "C++_Time_μs": 9274.362067, "Python_Time_μs": 36798.51988951365, "Speedup": 3.9677683083400423, "Performance_Ratio": "3.97x", "Category": "timeseries_ops"}, {"Operator": "ts_MeanChg", "C++_Time_μs": 15398.318967, "Python_Time_μs": 46888.5808562239, "Speedup": 3.0450454336418415, "Performance_Ratio": "3.05x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 13460.6593, "Python_Time_μs": 36124.42764764031, "Speedup": 2.6837041813873346, "Performance_Ratio": "2.68x", "Category": "timeseries_ops"}, {"Operator": "ts_Rank", "C++_Time_μs": 52307.683533, "Python_Time_μs": 129188.98854404688, "Speedup": 2.4697899011823723, "Performance_Ratio": "2.47x", "Category": "timeseries_ops"}, {"Operator": "ts_<PERSON>", "C++_Time_μs": 15120.774433, "Python_Time_μs": 36195.32228137056, "Speedup": 2.3937479156078725, "Performance_Ratio": "2.39x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_D", "C++_Time_μs": 64529.900333, "Python_Time_μs": 142606.3540702065, "Speedup": 2.209926767813074, "Performance_Ratio": "2.21x", "Category": "timeseries_ops"}, {"Operator": "ts_TransNorm", "C++_Time_μs": 67311.115067, "Python_Time_μs": 148315.1477140685, "Speedup": 2.2034272878474654, "Performance_Ratio": "2.20x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_C", "C++_Time_μs": 64508.566967, "Python_Time_μs": 139759.7542653481, "Speedup": 2.166530134467932, "Performance_Ratio": "2.17x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_B", "C++_Time_μs": 64470.397133, "Python_Time_μs": 135354.2149377366, "Speedup": 2.099478535218357, "Performance_Ratio": "2.10x", "Category": "timeseries_ops"}, {"Operator": "ts_Median", "C++_Time_μs": 63427.319933, "Python_Time_μs": 128852.1923745672, "Speedup": 2.031493566347707, "Performance_Ratio": "2.03x", "Category": "timeseries_ops"}, {"Operator": "ts_Quantile_A", "C++_Time_μs": 64432.578467, "Python_Time_μs": 129366.2323616445, "Speedup": 2.0077767402696933, "Performance_Ratio": "2.01x", "Category": "timeseries_ops"}, {"Operator": "ts_Entropy", "C++_Time_μs": 111539.832767, "Python_Time_μs": 169747.7798908949, "Speedup": 1.5218579379214938, "Performance_Ratio": "1.52x", "Category": "timeseries_ops"}, {"Operator": "ts_Decay2", "C++_Time_μs": 8205.5184, "Python_Time_μs": 8372.911034772793, "Speedup": 1.0204000072405897, "Performance_Ratio": "1.02x", "Category": "timeseries_ops"}, {"Operator": "ts_Decay", "C++_Time_μs": 8648.6318, "Python_Time_μs": 8320.034357408682, "Speedup": 0.962005846683019, "Performance_Ratio": "0.96x", "Category": "timeseries_ops"}, {"Operator": "Tot_Delta", "C++_Time_μs": 275.377267, "Python_Time_μs": 3303.9769157767296, "Speedup": 11.998001693352304, "Performance_Ratio": "12.00x", "Category": "panel_ops"}, {"Operator": "Tot_Sum", "C++_Time_μs": 3023.0569, "Python_Time_μs": 35835.076992710434, "Speedup": 11.853920775593219, "Performance_Ratio": "11.85x", "Category": "panel_ops"}, {"Operator": "Tot_Mean", "C++_Time_μs": 4142.2079, "Python_Time_μs": 35272.650669018425, "Speedup": 8.515422576693561, "Performance_Ratio": "8.52x", "Category": "panel_ops"}, {"Operator": "pn_CrossFit", "C++_Time_μs": 3922.0801, "Python_Time_μs": 30279.112700372934, "Speedup": 7.720166831975954, "Performance_Ratio": "7.72x", "Category": "panel_ops"}, {"Operator": "Tot_Stdev", "C++_Time_μs": 6563.271633, "Python_Time_μs": 47409.32400648793, "Speedup": 7.2234285974261345, "Performance_Ratio": "7.22x", "Category": "panel_ops"}, {"Operator": "Tot_ChgRate", "C++_Time_μs": 737.5462, "Python_Time_μs": 5015.970828632514, "Speedup": 6.800890342371114, "Performance_Ratio": "6.80x", "Category": "panel_ops"}, {"Operator": "Tot_Arg<PERSON>in", "C++_Time_μs": 10833.1605, "Python_Time_μs": 58072.63692840934, "Speedup": 5.360636623855923, "Performance_Ratio": "5.36x", "Category": "panel_ops"}, {"Operator": "Tot_Divide", "C++_Time_μs": 738.197933, "Python_Time_μs": 3873.9338827629886, "Speedup": 5.247825426738208, "Performance_Ratio": "5.25x", "Category": "panel_ops"}, {"Operator": "Tot_ArgMax", "C++_Time_μs": 10930.0775, "Python_Time_μs": 57316.983646402754, "Speedup": 5.2439686403324, "Performance_Ratio": "5.24x", "Category": "panel_ops"}, {"Operator": "pn_Winsor", "C++_Time_μs": 2864.8497, "Python_Time_μs": 11685.223070283731, "Speedup": 4.078825870091451, "Performance_Ratio": "4.08x", "Category": "panel_ops"}, {"Operator": "pn_TransStd", "C++_Time_μs": 3124.989833, "Python_Time_μs": 8647.489144156376, "Speedup": 2.767205529067197, "Performance_Ratio": "2.77x", "Category": "panel_ops"}, {"Operator": "pn_Mean", "C++_Time_μs": 1246.384467, "Python_Time_μs": 3285.067683706681, "Speedup": 2.635677650583783, "Performance_Ratio": "2.64x", "Category": "panel_ops"}, {"Operator": "Tot_Rank", "C++_Time_μs": 57366.784867, "Python_Time_μs": 149987.79545227686, "Speedup": 2.6145407277052524, "Performance_Ratio": "2.61x", "Category": "panel_ops"}, {"Operator": "pn_FillMin", "C++_Time_μs": 1117.977367, "Python_Time_μs": 2203.981764614582, "Speedup": 1.9714010584389425, "Performance_Ratio": "1.97x", "Category": "panel_ops"}, {"Operator": "pn_FillMax", "C++_Time_μs": 1124.888867, "Python_Time_μs": 2179.3058142066, "Speedup": 1.9373521048516167, "Performance_Ratio": "1.94x", "Category": "panel_ops"}, {"Operator": "pn_Cut", "C++_Time_μs": 15699.286667, "Python_Time_μs": 29502.197665472824, "Speedup": 1.8792062525672986, "Performance_Ratio": "1.88x", "Category": "panel_ops"}, {"Operator": "To<PERSON>_<PERSON>", "C++_Time_μs": 19157.532667, "Python_Time_μs": 34766.233805567026, "Speedup": 1.8147552928595, "Performance_Ratio": "1.81x", "Category": "panel_ops"}, {"Operator": "pn_Rank2", "C++_Time_μs": 10874.594967, "Python_Time_μs": 19182.226806879044, "Speedup": 1.7639486220028742, "Performance_Ratio": "1.76x", "Category": "panel_ops"}, {"Operator": "Tot_Max", "C++_Time_μs": 20963.721467, "Python_Time_μs": 34921.01213584343, "Speedup": 1.6657830619823049, "Performance_Ratio": "1.67x", "Category": "panel_ops"}, {"Operator": "pn_Rank", "C++_Time_μs": 12681.678467, "Python_Time_μs": 20878.318635125954, "Speedup": 1.6463371697567544, "Performance_Ratio": "1.65x", "Category": "panel_ops"}, {"Operator": "pn_RankCentered", "C++_Time_μs": 14428.2012, "Python_Time_μs": 23323.32997272412, "Speedup": 1.6165098926347188, "Performance_Ratio": "1.62x", "Category": "panel_ops"}, {"Operator": "pn_TransNorm", "C++_Time_μs": 27516.178467, "Python_Time_μs": 41800.14198645949, "Speedup": 1.5191114578861367, "Performance_Ratio": "1.52x", "Category": "panel_ops"}, {"Operator": "pn_Stand", "C++_Time_μs": 9888.932033, "Python_Time_μs": 6849.302320430676, "Speedup": 0.692623055510354, "Performance_Ratio": "0.69x", "Category": "panel_ops"}, {"Operator": "pn_GroupNeutral", "C++_Time_μs": 5279.3566, "Python_Time_μs": 87437.45079264045, "Speedup": 16.562141453494625, "Performance_Ratio": "16.56x", "Category": "group_ops"}, {"Operator": "pn_GroupRank", "C++_Time_μs": 13342.681733, "Python_Time_μs": 150117.5681129098, "Speedup": 11.250929244728152, "Performance_Ratio": "11.25x", "Category": "group_ops"}, {"Operator": "pn_GroupNorm", "C++_Time_μs": 27731.6196, "Python_Time_μs": 203095.62167773643, "Speedup": 7.323611985422461, "Performance_Ratio": "7.32x", "Category": "group_ops"}]}