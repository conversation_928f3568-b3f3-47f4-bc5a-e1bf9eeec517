Feature Operators 性能比较汇总报告 (Python vs C++)
================================================================================

测试时间: 20250528_154802
总测试分类数: 8
总测试算子数: 91

整体性能统计:
--------------------------------------------------
C++总平均执行时间: 11568.572 μs
Python总平均执行时间: 49139.921 μs
总平均加速比: 10.21x
最大加速比: 67.75x (ts_Regression_A)
最小加速比: 0.02x (Power)

各分类性能汇总:
--------------------------------------------------------------------------------
分类              算子数      C++平均(μs)    Python平均(μs)    平均加速比     
--------------------------------------------------------------------------------
core_math       17       2149.199     2291.812        2.23      
logical_ops     4        469.762      12619.837       26.82     
comparison_ops  6        303.672      3613.263        12.10     
data_utils      3        124.754      1615.972        14.70     
reduction_ops   2        304.820      12032.375       39.47     
timeseries_ops  33       21998.305    97741.166       13.77     
panel_ops       23       10400.912    28938.752       4.29      
group_ops       3        15451.219    146883.547      11.71     

前10名最快的算子 (按加速比排序):
--------------------------------------------------------------------------------
排名   算子                   分类              C++时间(μs)    Python时间(μs)    加速比       
--------------------------------------------------------------------------------
1    <USER>      <GROUP>  2204.600     149364.826      67.75     
2    ts_Regression_B      timeseries_ops  2562.252     150751.522      58.84     
3    ts_Regression_D      timeseries_ops  2854.745     153490.456      53.77     
4    ts_Regression_C      timeseries_ops  2871.468     152903.428      53.25     
5    ts_Corr              timeseries_ops  5029.599     200357.308      39.84     
6    Min                  reduction_ops   304.536      12043.764       39.55     
7    Max                  reduction_ops   305.104      12020.987       39.40     
8    ts_Cov               timeseries_ops  2760.712     108530.661      39.31     
9    Xor                  logical_ops     503.120      16427.064       32.65     
10   ts_Partial_corr      timeseries_ops  21059.485    617514.468      29.32     

后10名最慢的算子 (按加速比排序):
--------------------------------------------------------------------------------
排名   算子                   分类              C++时间(μs)    Python时间(μs)    加速比       
--------------------------------------------------------------------------------
1    <USER>                <GROUP>       15382.494    259.845         0.02      
2    pn_Stand             panel_ops       9888.932     6849.302        0.69      
3    ts_Decay             timeseries_ops  8648.632     8320.034        0.96      
4    ts_Decay2            timeseries_ops  8205.518     8372.911        1.02      
5    Sqrt                 core_math       630.727      723.590         1.15      
6    Round                core_math       168.206      200.500         1.19      
7    inv                  core_math       370.939      456.931         1.23      
8    Exp                  core_math       9092.608     11549.172       1.27      
9    Floor                core_math       162.825      211.239         1.30      
10   Ceil                 core_math       164.515      214.780         1.31      

================================================================================
按分类详细性能列表:
================================================================================

COMPARISON_OPS (6 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
MEthan               275.811      4284.767        15.54     
LEthan               276.794      4064.918        14.69     
Lthan                302.909      4159.396        13.73     
Mthan                309.120      4186.579        13.54     
UnEqual              331.985      3454.274        10.40     
Equal                325.413      1529.646        4.70      

CORE_MATH (17 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Abs                  224.924      1995.249        8.87      
Sign                 551.997      2665.960        4.83      
SignedPower          2652.455     7845.755        2.96      
Minus                222.146      508.196         2.29      
Multiply             222.539      505.724         2.27      
Add                  239.809      514.994         2.15      
Softsign             1046.921     2228.254        2.13      
Divide               374.812      649.726         1.73      
Log                  4860.605     8172.959        1.68      
Reverse              167.867      257.929         1.54      
Ceil                 164.515      214.780         1.31      
Floor                162.825      211.239         1.30      
Exp                  9092.608     11549.172       1.27      
inv                  370.939      456.931         1.23      
Round                168.206      200.500         1.19      
Sqrt                 630.727      723.590         1.15      
Power                15382.494    259.845         0.02      

DATA_UTILS (3 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
getInf               103.868      2093.038        20.15     
getNan               105.479      2120.449        20.10     
FilterInf            164.915      634.430         3.85      

GROUP_OPS (3 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
pn_GroupNeutral      5279.357     87437.451       16.56     
pn_GroupRank         13342.682    150117.568      11.25     
pn_GroupNorm         27731.620    203095.622      7.32      

LOGICAL_OPS (4 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Xor                  503.120      16427.064       32.65     
Not                  369.217      9690.921        26.25     
And                  505.805      12504.628       24.72     
Or                   500.905      11856.735       23.67     

PANEL_OPS (23 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Tot_Delta            275.377      3303.977        12.00     
Tot_Sum              3023.057     35835.077       11.85     
Tot_Mean             4142.208     35272.651       8.52      
pn_CrossFit          3922.080     30279.113       7.72      
Tot_Stdev            6563.272     47409.324       7.22      
Tot_ChgRate          737.546      5015.971        6.80      
Tot_ArgMin           10833.160    58072.637       5.36      
Tot_Divide           738.198      3873.934        5.25      
Tot_ArgMax           10930.077    57316.984       5.24      
pn_Winsor            2864.850     11685.223       4.08      
pn_TransStd          3124.990     8647.489        2.77      
pn_Mean              1246.384     3285.068        2.64      
Tot_Rank             57366.785    149987.795      2.61      
pn_FillMin           1117.977     2203.982        1.97      
pn_FillMax           1124.889     2179.306        1.94      
pn_Cut               15699.287    29502.198       1.88      
Tot_Min              19157.533    34766.234       1.81      
pn_Rank2             10874.595    19182.227       1.76      
Tot_Max              20963.721    34921.012       1.67      
pn_Rank              12681.678    20878.319       1.65      
pn_RankCentered      14428.201    23323.330       1.62      
pn_TransNorm         27516.178    41800.142       1.52      
pn_Stand             9888.932     6849.302        0.69      

REDUCTION_OPS (2 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
Min                  304.536      12043.764       39.55     
Max                  305.104      12020.987       39.40     

TIMESERIES_OPS (33 个算子):
------------------------------------------------------------
算子                   C++时间(μs)    Python时间(μs)    加速比       
------------------------------------------------------------
ts_Regression_A      2204.600     149364.826      67.75     
ts_Regression_B      2562.252     150751.522      58.84     
ts_Regression_D      2854.745     153490.456      53.77     
ts_Regression_C      2871.468     152903.428      53.25     
ts_Corr              5029.599     200357.308      39.84     
ts_Cov               2760.712     108530.661      39.31     
ts_Partial_corr      21059.485    617514.468      29.32     
ts_Sum               3012.202     35322.202       11.73     
ts_Delta             277.449      2457.605        8.86      
ts_Mean              4157.204     36122.904       8.69      
ts_Delay             275.064      2142.438        7.79      
ts_Scale             9347.228     70533.337       7.55      
ts_Stdev             6570.751     47429.920       7.22      
ts_Argmin            9143.507     51186.381       5.60      
ts_Argmax            9314.408     50438.656       5.42      
ts_Skewness          8578.384     46339.022       5.40      
ts_ChgRate           776.487      4104.720        5.29      
ts_Divide            745.920      3069.170        4.11      
ts_Kurtosis          11766.936    47508.987       4.04      
ts_Product           9274.362     36798.520       3.97      
ts_MeanChg           15398.319    46888.581       3.05      
ts_Min               13460.659    36124.428       2.68      
ts_Rank              52307.684    129188.989      2.47      
ts_Max               15120.774    36195.322       2.39      
ts_Quantile_D        64529.900    142606.354      2.21      
ts_TransNorm         67311.115    148315.148      2.20      
ts_Quantile_C        64508.567    139759.754      2.17      
ts_Quantile_B        64470.397    135354.215      2.10      
ts_Median            63427.320    128852.192      2.03      
ts_Quantile_A        64432.578    129366.232      2.01      
ts_Entropy           111539.833   169747.780      1.52      
ts_Decay2            8205.518     8372.911        1.02      
ts_Decay             8648.632     8320.034        0.96      
